* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* 上传区域样式 */
.upload-area {
    border: 3px dashed #667eea;
    border-radius: 15px;
    padding: 60px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #764ba2;
    background-color: #f8f9ff;
}

.upload-area.dragover {
    border-color: #764ba2;
    background-color: #f0f2ff;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 20px;
}

.upload-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
}

.upload-content p {
    color: #666;
    margin-bottom: 20px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #dee2e6;
}

.btn-secondary:hover {
    background: #e9ecef;
}

/* 进度条样式 */
.progress-container {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

#progress-text {
    text-align: center;
    color: #666;
}

/* 杂志区域样式 */
.magazine-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
}

.magazine-controls {
    display: flex;
    gap: 10px;
}

.flipbook-container {
    display: flex;
    justify-content: center;
    margin: 30px 0;
    min-height: 600px;
}

.flipbook {
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
    border-radius: 10px;
    overflow: hidden;
}

.flipbook .page {
    background-color: white;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border: 1px solid #ddd;
}

.magazine-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    flex-wrap: wrap;
    gap: 15px;
}

.page-info {
    font-size: 1.1rem;
    font-weight: 500;
    color: #333;
}

.navigation-controls {
    display: flex;
    gap: 10px;
}

.nav-btn {
    padding: 10px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: white;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .section {
        padding: 20px;
    }
    
    .upload-area {
        padding: 40px 15px;
    }
    
    .magazine-header {
        flex-direction: column;
        text-align: center;
    }
    
    .magazine-footer {
        flex-direction: column;
        text-align: center;
    }
    
    .flipbook {
        max-width: 100%;
        height: auto !important;
    }
}

/* 全屏模式 */
.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #000;
    z-index: 10000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.fullscreen .flipbook {
    max-width: 90vw;
    max-height: 90vh;
}
