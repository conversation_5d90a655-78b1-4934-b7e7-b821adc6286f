<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF转在线杂志工具</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-book-open"></i> PDF转在线杂志工具</h1>
            <p>将您的PDF文件转换为具有翻页效果的在线杂志</p>
        </header>

        <!-- 上传区域 -->
        <div id="upload-section" class="section">
            <div class="upload-area" id="upload-area">
                <div class="upload-content">
                    <i class="fas fa-cloud-upload-alt upload-icon"></i>
                    <h3>拖拽PDF文件到这里或点击选择</h3>
                    <p>支持最大50MB的PDF文件</p>
                    <input type="file" id="pdf-input" accept=".pdf" style="display: none;">
                    <button class="btn btn-primary" onclick="document.getElementById('pdf-input').click()">
                        <i class="fas fa-file-pdf"></i> 选择PDF文件
                    </button>
                </div>
            </div>
            
            <div id="upload-progress" class="progress-container" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <p id="progress-text">正在上传...</p>
            </div>
        </div>

        <!-- 杂志展示区域 -->
        <div id="magazine-section" class="section" style="display: none;">
            <div class="magazine-header">
                <h2 id="magazine-title">在线杂志</h2>
                <div class="magazine-controls">
                    <button class="btn btn-secondary" onclick="resetUpload()">
                        <i class="fas fa-upload"></i> 上传新文件
                    </button>
                    <button class="btn btn-secondary" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i> 全屏
                    </button>
                </div>
            </div>
            
            <div class="flipbook-container">
                <div id="flipbook" class="flipbook"></div>
            </div>
            
            <div class="magazine-footer">
                <div class="page-info">
                    <span id="current-page">1</span> / <span id="total-pages">1</span>
                </div>
                <div class="navigation-controls">
                    <button class="nav-btn" id="prev-btn" onclick="prevPage()">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </button>
                    <button class="nav-btn" id="next-btn" onclick="nextPage()">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 加载提示 -->
        <div id="loading-overlay" class="loading-overlay" style="display: none;">
            <div class="loading-content">
                <div class="spinner"></div>
                <h3>正在转换PDF...</h3>
                <p>请稍候，这可能需要几分钟时间</p>
            </div>
        </div>
    </div>

    <!-- 引入Turn.js库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/turn.js/3/turn.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
