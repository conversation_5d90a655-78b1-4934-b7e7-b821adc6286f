let currentMagazine = null;
let flipbook = null;

// DOM元素
const uploadArea = document.getElementById('upload-area');
const pdfInput = document.getElementById('pdf-input');
const uploadSection = document.getElementById('upload-section');
const magazineSection = document.getElementById('magazine-section');
const loadingOverlay = document.getElementById('loading-overlay');
const progressContainer = document.getElementById('upload-progress');
const progressFill = document.getElementById('progress-fill');
const progressText = document.getElementById('progress-text');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

function setupEventListeners() {
    // 文件输入事件
    pdfInput.addEventListener('change', handleFileSelect);
    
    // 拖拽事件
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    
    // 点击上传区域
    uploadArea.addEventListener('click', () => {
        pdfInput.click();
    });
}

function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type === 'application/pdf') {
            uploadPDF(file);
        } else {
            alert('请选择PDF文件');
        }
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        uploadPDF(file);
    }
}

function uploadPDF(file) {
    const formData = new FormData();
    formData.append('pdf', file);
    
    // 显示进度条
    progressContainer.style.display = 'block';
    loadingOverlay.style.display = 'flex';
    
    const xhr = new XMLHttpRequest();
    
    // 上传进度
    xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            progressFill.style.width = percentComplete + '%';
            progressText.textContent = `正在上传... ${Math.round(percentComplete)}%`;
        }
    });
    
    // 请求完成
    xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
                progressText.textContent = '转换完成！';
                setTimeout(() => {
                    loadMagazine(response);
                }, 1000);
            } else {
                alert('转换失败: ' + response.error);
                hideLoading();
            }
        } else {
            const error = JSON.parse(xhr.responseText);
            alert('上传失败: ' + error.error);
            hideLoading();
        }
    });
    
    // 请求错误
    xhr.addEventListener('error', () => {
        alert('上传失败，请检查网络连接');
        hideLoading();
    });
    
    xhr.open('POST', '/upload');
    xhr.send(formData);
}

function loadMagazine(data) {
    currentMagazine = data;
    
    // 隐藏上传区域，显示杂志区域
    uploadSection.style.display = 'none';
    magazineSection.style.display = 'block';
    
    // 更新页面信息
    document.getElementById('total-pages').textContent = data.pages;
    document.getElementById('current-page').textContent = '1';
    
    // 创建翻页书
    createFlipbook(data.images);
    
    hideLoading();
}

function createFlipbook(images) {
    const flipbookElement = document.getElementById('flipbook');
    flipbookElement.innerHTML = '';
    
    // 添加页面
    images.forEach((imageSrc, index) => {
        const page = document.createElement('div');
        page.className = 'page';
        page.style.backgroundImage = `url('${imageSrc}')`;
        flipbookElement.appendChild(page);
    });
    
    // 初始化Turn.js
    $(flipbookElement).turn({
        width: 800,
        height: 600,
        autoCenter: true,
        gradients: true,
        elevation: 50,
        when: {
            turned: function(event, page, view) {
                document.getElementById('current-page').textContent = page;
                updateNavigationButtons(page, images.length);
            }
        }
    });
    
    flipbook = $(flipbookElement);
    updateNavigationButtons(1, images.length);
}

function updateNavigationButtons(currentPage, totalPages) {
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    
    prevBtn.disabled = currentPage <= 1;
    nextBtn.disabled = currentPage >= totalPages;
}

function prevPage() {
    if (flipbook) {
        flipbook.turn('previous');
    }
}

function nextPage() {
    if (flipbook) {
        flipbook.turn('next');
    }
}

function resetUpload() {
    // 重置状态
    currentMagazine = null;
    flipbook = null;
    
    // 重置UI
    uploadSection.style.display = 'block';
    magazineSection.style.display = 'none';
    progressContainer.style.display = 'none';
    progressFill.style.width = '0%';
    progressText.textContent = '正在上传...';
    pdfInput.value = '';
    
    // 清理flipbook
    const flipbookElement = document.getElementById('flipbook');
    if (flipbookElement) {
        $(flipbookElement).turn('destroy');
        flipbookElement.innerHTML = '';
    }
}

function toggleFullscreen() {
    const flipbookContainer = document.querySelector('.flipbook-container');
    
    if (!document.fullscreenElement) {
        flipbookContainer.requestFullscreen().then(() => {
            flipbookContainer.classList.add('fullscreen');
            // 重新调整flipbook大小
            if (flipbook) {
                setTimeout(() => {
                    flipbook.turn('size', window.innerWidth * 0.9, window.innerHeight * 0.9);
                }, 100);
            }
        });
    } else {
        document.exitFullscreen().then(() => {
            flipbookContainer.classList.remove('fullscreen');
            // 恢复原始大小
            if (flipbook) {
                setTimeout(() => {
                    flipbook.turn('size', 800, 600);
                }, 100);
            }
        });
    }
}

function hideLoading() {
    loadingOverlay.style.display = 'none';
    progressContainer.style.display = 'none';
}

// 键盘导航
document.addEventListener('keydown', (e) => {
    if (currentMagazine && flipbook) {
        if (e.key === 'ArrowLeft') {
            prevPage();
        } else if (e.key === 'ArrowRight') {
            nextPage();
        } else if (e.key === 'Escape' && document.fullscreenElement) {
            toggleFullscreen();
        }
    }
});

// 全屏状态变化监听
document.addEventListener('fullscreenchange', () => {
    const flipbookContainer = document.querySelector('.flipbook-container');
    if (!document.fullscreenElement) {
        flipbookContainer.classList.remove('fullscreen');
        if (flipbook) {
            setTimeout(() => {
                flipbook.turn('size', 800, 600);
            }, 100);
        }
    }
});
