const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs-extra');
const pdf = require('pdf-poppler');
const sharp = require('sharp');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));
app.use('/uploads', express.static('uploads'));

// 确保上传目录存在
const uploadDir = path.join(__dirname, 'uploads');
const tempDir = path.join(__dirname, 'temp');
fs.ensureDirSync(uploadDir);
fs.ensureDirSync(tempDir);

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('只允许上传PDF文件'), false);
    }
  },
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB限制
  }
});

// 主页路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// PDF上传和转换路由
app.post('/upload', upload.single('pdf'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: '请选择PDF文件' });
    }

    const pdfPath = req.file.path;
    const outputDir = path.join(uploadDir, req.file.filename.replace('.pdf', ''));
    
    // 确保输出目录存在
    fs.ensureDirSync(outputDir);

    console.log('开始转换PDF:', pdfPath);

    // 配置pdf-poppler选项
    const options = {
      format: 'jpeg',
      out_dir: outputDir,
      out_prefix: 'page',
      page: null // 转换所有页面
    };

    // 转换PDF为图片
    await pdf.convert(pdfPath, options);

    // 获取生成的图片文件
    const files = fs.readdirSync(outputDir);
    const imageFiles = files
      .filter(file => file.endsWith('.jpg'))
      .sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)[0]);
        const numB = parseInt(b.match(/\d+/)[0]);
        return numA - numB;
      });

    // 优化图片质量和大小
    const optimizedImages = [];
    for (const imageFile of imageFiles) {
      const inputPath = path.join(outputDir, imageFile);
      const outputPath = path.join(outputDir, `optimized_${imageFile}`);
      
      await sharp(inputPath)
        .jpeg({ quality: 85 })
        .resize(1200, null, { 
          withoutEnlargement: true,
          fit: 'inside'
        })
        .toFile(outputPath);
      
      optimizedImages.push(`optimized_${imageFile}`);
      
      // 删除原始图片
      fs.unlinkSync(inputPath);
    }

    // 删除临时PDF文件
    fs.unlinkSync(pdfPath);

    const magazineId = req.file.filename.replace('.pdf', '');
    
    res.json({
      success: true,
      magazineId: magazineId,
      pages: optimizedImages.length,
      images: optimizedImages.map(img => `/uploads/${magazineId}/${img}`)
    });

  } catch (error) {
    console.error('转换错误:', error);
    res.status(500).json({ error: 'PDF转换失败: ' + error.message });
  }
});

// 获取杂志信息
app.get('/magazine/:id', (req, res) => {
  const magazineId = req.params.id;
  const magazineDir = path.join(uploadDir, magazineId);
  
  if (!fs.existsSync(magazineDir)) {
    return res.status(404).json({ error: '杂志不存在' });
  }

  const files = fs.readdirSync(magazineDir);
  const imageFiles = files
    .filter(file => file.startsWith('optimized_') && file.endsWith('.jpg'))
    .sort((a, b) => {
      const numA = parseInt(a.match(/\d+/)[0]);
      const numB = parseInt(b.match(/\d+/)[0]);
      return numA - numB;
    });

  res.json({
    magazineId: magazineId,
    pages: imageFiles.length,
    images: imageFiles.map(img => `/uploads/${magazineId}/${img}`)
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: '文件大小超过限制(50MB)' });
    }
  }
  res.status(500).json({ error: error.message });
});

app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
});
